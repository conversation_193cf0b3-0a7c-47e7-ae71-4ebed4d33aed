# Use a block to apply 'become: true' to multiple tasks at once.
- name: Configure system for rootless Dock<PERSON>
  become: true
  block:
    - name: Check current Docker installation
      ansible.builtin.command: docker --version
      register: docker_current
      ignore_errors: true
      changed_when: false

    - name: Display current Docker version
      ansible.builtin.debug:
        msg: "Current Docker: {{ docker_current.stdout | default('Not installed') }}"

    - name: Clean up existing Docker repositories
      ansible.builtin.shell: |
        find /etc/apt/sources.list.d/ -name "*docker*" -delete
        sed -i '/docker/d' /etc/apt/sources.list
      ignore_errors: true
      changed_when: false

    - name: Update APT cache after cleanup
      ansible.builtin.apt:
        update_cache: true
        cache_valid_time: 0

    # Update cache and install prerequisites in one step for efficiency.
    - name: Install prerequisites
      ansible.builtin.apt:
        name:
          - ca-certificates
          - uidmap
          - dbus-user-session
        state: present

    - name: Create keyring directory for APT keys
      ansible.builtin.file:
        path: /etc/apt/keyrings
        state: directory
        mode: '0755'

    # 'force: false' prevents re-downloading the key if it already exists.
    - name: Download Docker GPG key from Artifactory
      ansible.builtin.get_url:
        url: https://artifactory.dach041.dachser.com/artifactory/ubuntu-docker/gpg
        dest: /etc/apt/keyrings/docker.asc
        mode: '0644'
        force: false 

    - name: Remove conflicting Docker repositories
      ansible.builtin.file:
        path: "{{ item }}"
        state: absent
      loop:
        - /etc/apt/sources.list.d/docker.list
        - /etc/apt/sources.list.d/docker-ce.list

    - name: Add Docker repository from Artifactory
      ansible.builtin.apt_repository:
        repo: "deb [arch=amd64 signed-by=/etc/apt/keyrings/docker.asc] https://artifactory.dach041.dachser.com/artifactory/ubuntu-docker/ {{ ansible_distribution_release }} stable"
        state: present
        filename: docker

    - name: Install Docker packages
      ansible.builtin.apt:
        name:
          - docker-ce
          - docker-ce-cli
          - containerd.io
          - docker-buildx-plugin
          - docker-compose-plugin
        state: present

    - name: Disable and stop system-wide (root) Docker services
      ansible.builtin.service:
        name: "{{ item }}"
        state: stopped
        enabled: false
      loop:
        - docker.service
        - docker.socket

    - name: Remove system-wide Docker socket to avoid conflicts
      ansible.builtin.file:
        path: /var/run/docker.sock
        state: absent

    # 'create: true' ensures this task doesn't fail if the files don't exist yet.
    - name: Configure subuid and subgid for the user
      ansible.builtin.lineinfile:
        path: "{{ item }}"
        line: "{{ ansible_user_id }}:100000:65536"
        state: present
        create: true
        regexp: '^{{ ansible_user_id }}:'
      loop:
        - /etc/subuid
        - /etc/subgid

# This task now only runs if the rootless setup hasn't been completed before.
# It checks for the existence of the user's docker service file.
- name: Run rootless setup script for the user
  become: false
  ansible.builtin.command: dockerd-rootless-setuptool.sh install
  args:
    creates: "{{ ansible_env.HOME }}/.config/systemd/user/docker.service"
  changed_when: false # The script handles its own change reporting.

- name: Verify Docker Compose installation
  become: false
  block:
    - name: Check Docker Compose version
      ansible.builtin.command: docker compose version
      register: docker_compose_version
      changed_when: false

    - name: Display Docker Compose version
      ansible.builtin.debug:
        msg: "Docker Compose is installed: {{ docker_compose_version.stdout }}"
