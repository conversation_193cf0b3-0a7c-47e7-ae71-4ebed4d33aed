---
- name: Verify Rootless Docker Installation
  hosts: docker_hosts
  become: false # All checks must run as the regular user
  gather_facts: true # Required to get user environment variables

  tasks:
    - name: Check Docker client and server version
      ansible.builtin.command: docker version --format '{% raw %}{{json .}}{% endraw %}'
      register: docker_version_output
      changed_when: false

    - name: Display Docker version info
      ansible.builtin.debug:
        msg:
          - "Client Version: {{ (docker_version_output.stdout | from_json).Client.Version }}"
          - "Server Version: {{ (docker_version_output.stdout | from_json).Server.Version }}"

    - name: Get Docker system information
      ansible.builtin.command: docker info --format '{% raw %}{{json .}}{% endraw %}'
      register: docker_info_output
      changed_when: false

    - name: Parse Docker info JSON from stdout
      ansible.builtin.set_fact:
        docker_info: "{{ docker_info_output.stdout | from_json }}"

    - name: Check if running in rootless context
      ansible.builtin.shell: docker context show
      register: docker_context
      changed_when: false
      
    - name: Verify Dock<PERSON> is running in rootless mode
      ansible.builtin.debug:
        msg: "✅ Docker is running in rootless mode"
      when: docker_context.stdout == "rootless"

    - name: Run hello-world container to test functionality
      community.docker.docker_container:
        name: hello-world-test
        image: docker-all.dach041.dachser.com/hello-world
        state: started
        auto_remove: true # Clean up the container after it runs
        docker_host: "unix://{{ ansible_env.XDG_RUNTIME_DIR }}/docker.sock"
      register: hello_world_result

    - name: Final verification summary
      ansible.builtin.debug:
        msg: "SUCCESS: Rootless Docker installation has been successfully verified! The hello-world container ran without issues."
      when: not hello_world_result.failed
