stages:
  - .pre
  - build
  - build-images
  - .post

default:
  tags:
    - openshift

workflow:
  rules:
    - if: $IMAGE_TAG
      when: always
    - when: never

variables:
  # Base images
  BUILDER_SSH: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/ssh:latest
  # Artifactory
  JFROG_PROJECT: bint

  # Docker
  BASE_DOCKER_REPO: "docker-artifactory.dach041.dachser.com"
  IMAGE_TARGET_REPO: "bint-docker-dfe"
  IMAGE_GROUP: "ci"
include:
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/build-info@main
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/normalize-image@main
    inputs:
      image-tag: $IMAGE_TAG
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/docker@main
    inputs:
      image_name: $DOCKER_IMAGE
      image_tag: $DOCKER_TAG

normalize-image-name:
  rules:

build-image:
  rules:
  script:
    - !reference [.prepare-dachser-certificates, script]
    - cp "$PIP_CONFIG_FILE" ./pip.ini
    - buildah build --build-arg PIP_INI=pip.ini --tls-verify=false -t $FQ_IMAGE_NAME
    - buildah push --tls-verify=false  $FQ_IMAGE_NAME docker://$FQ_IMAGE_NAME
    - buildah rmi $FQ_IMAGE_NAME
    - buildah manifest create local-manifest-list
    - buildah manifest add --tls-verify=false local-manifest-list docker://$FQ_IMAGE_NAME
    - buildah manifest inspect local-manifest-list
    - IMAGE_DIGEST=$(buildah manifest inspect local-manifest-list | grep -i '"digest"' | awk -F'"' '{print $4}')
    - echo $IMAGE_DIGEST
    - echo "$FQ_IMAGE_NAME@${IMAGE_DIGEST}" >> image-file-details
    - echo "IMAGE_DIGEST=${IMAGE_DIGEST}" > info.env


publish-build-info:
  rules:
