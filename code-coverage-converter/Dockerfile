FROM docker-all.dach041.dachser.com/python:3.9-slim

WORKDIR /app

ARG PIP_INI
COPY ${PIP_INI} /etc/pip.conf

COPY certificates/ /certificates/

# Install the necessary dependencies
RUN pip install --no-cache-dir --upgrade pip && \
pip install --no-cache-dir lxml

# Copy the conversion script
COPY convert_reports.py /app/convert_reports.py

# Make the script executable
RUN chmod +x /app/convert_reports.py