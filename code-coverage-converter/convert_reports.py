import json
import os
from lxml import etree

def convert_pmd_to_codequality(pmd_file, output_file):
    if not os.path.exists(pmd_file):
        print(f"PMD file {pmd_file} not found")
        return False
        
    try:
        tree = etree.parse(pmd_file)
        root = tree.getroot()
        
        issues = []
        
        for file_elem in root.findall('.//file'):
            file_path = file_elem.get('name', '')
            for violation in file_elem.findall('.//violation'):
                line = int(violation.get('beginline', 1))
                message = violation.text.strip() if violation.text else "PMD violation"
                rule = violation.get('rule', 'unknown')
                
                issues.append({
                    "description": f"[PMD] {message}",
                    "fingerprint": f"{file_path}-{line}-{rule}",
                    "severity": "major",
                    "location": {
                        "path": file_path,
                        "lines": {
                            "begin": line
                        }
                    }
                })
        
        with open(output_file, 'w') as f:
            json.dump(issues, f)
        return True
    except Exception as e:
        print(f"Error converting PMD file: {e}")
        return False

def convert_checkstyle_to_codequality(checkstyle_file, output_file):
    if not os.path.exists(checkstyle_file):
        print(f"Checkstyle file {checkstyle_file} not found")
        return False
        
    try:
        tree = etree.parse(checkstyle_file)
        root = tree.getroot()
        
        issues = []
        
        for file_elem in root.findall('.//file'):
            file_path = file_elem.get('name', '')
            for error in file_elem.findall('.//error'):
                line = int(error.get('line', 1))
                message = error.get('message', 'Checkstyle violation')
                source = error.get('source', 'unknown')
                
                issues.append({
                    "description": f"[Checkstyle] {message}",
                    "fingerprint": f"{file_path}-{line}-{source}",
                    "severity": "minor",
                    "location": {
                        "path": file_path,
                        "lines": {
                            "begin": line
                        }
                    }
                })
        
        with open(output_file, 'w') as f:
            json.dump(issues, f)
        return True
    except Exception as e:
        print(f"Error converting Checkstyle file: {e}")
        return False

def convert_spotbugs_to_codequality(spotbugs_file, output_file):
    if not os.path.exists(spotbugs_file):
        print(f"SpotBugs file {spotbugs_file} not found")
        return False
        
    try:
        tree = etree.parse(spotbugs_file)
        root = tree.getroot()
        
        issues = []
        
        for bug in root.findall('.//BugInstance'):
            category = bug.get('category', 'unknown')
            type_name = bug.get('type', 'unknown')
            priority = int(bug.get('priority', 3))
            
            source_line = bug.find('.//SourceLine')
            if source_line is not None:
                file_path = source_line.get('sourcepath', '')
                start_line = int(source_line.get('start', 1))
                
                message_node = bug.find('.//LongMessage')
                message = message_node.text if message_node is not None and message_node.text else f"SpotBugs issue: {type_name}"
                
                issues.append({
                    "description": f"[SpotBugs] {message}",
                    "fingerprint": f"{file_path}-{start_line}-{type_name}",
                    "severity": "critical" if priority == 1 else "major" if priority == 2 else "minor",
                    "location": {
                        "path": file_path,
                        "lines": {
                            "begin": start_line
                        }
                    }
                })
        
        with open(output_file, 'w') as f:
            json.dump(issues, f)
        return True
    except Exception as e:
        print(f"Error converting SpotBugs file: {e}")
        return False

# Create an empty issues array as a fallback
def create_empty_report(output_file):
    with open(output_file, 'w') as f:
        json.dump([], f)

if __name__ == "__main__":
    # Try to convert all reports, create empty ones if they fail
    pmd_success = convert_pmd_to_codequality("target/pmd.xml", "gl-code-quality-pmd.json")
    if not pmd_success:
        create_empty_report("gl-code-quality-pmd.json")
        
    checkstyle_success = convert_checkstyle_to_codequality("target/checkstyle-result.xml", "gl-code-quality-checkstyle.json")
    if not checkstyle_success:
        create_empty_report("gl-code-quality-checkstyle.json")
        
    spotbugs_success = convert_spotbugs_to_codequality("target/spotbugsXml.xml", "gl-code-quality-spotbugs.json")
    if not spotbugs_success:
        create_empty_report("gl-code-quality-spotbugs.json")
    
    # Combine all reports into one
    combined_issues = []
    for filename in ["gl-code-quality-pmd.json", "gl-code-quality-checkstyle.json", "gl-code-quality-spotbugs.json"]:
        try:
            with open(filename, 'r') as f:
                issues = json.load(f)
                combined_issues.extend(issues)
        except:
            pass
            
    with open("gl-code-quality.json", 'w') as f:
        json.dump(combined_issues, f)
