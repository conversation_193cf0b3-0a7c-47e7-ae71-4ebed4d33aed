include:
  - local: ci-base.gitlab-ci.yml
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-info@main
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-build@fmain
    inputs:
      build-number: $BUILD_NUMBER
      build-name: $BUILD_NAME
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-test@main
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-code-analysis@main
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-code-coverage-report@main

    