spec:
  inputs:
    coverage-stage:
      default: post-test
      description: "The stage in which the coverage job will run"
    report-job-name:
      default: "maven-build"
      description: "Name of the test job that generates coverage data"
    jacoco-report-path:
      default: "target/site/jacoco/index.html"
      description: "Path to the JaCoCo index.html file containing coverage data"
    jacoco-xml-path:
      default: "target/site/jacoco/jacoco.xml"
      description: "Path to the JaCoCo XML file for conversion to Cobertura format"
    source-path:
      default: "src/main/java"
      description: "Path to the source code for coverage mapping"
    artifacts-expiry:
      default: "1 week"
      description: "How long to keep the coverage artifacts"
    maven-image-name:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/maven:jdk-21-v1.1.0
      description: "Maven image to use for coverage reporting"
    visualize-stage:
      default: .post
      description: "The stage in which the visualization job will run"
    coverage-job-name:
      default: "coverage-report"
      description: "Name of the coverage job that generates JaCoCo reports"
    code-quality-stage:
      default: post-test
      description: "The stage in which the code quality job will run"
    formatter-image-name:
      default: bint-docker-dfe.docker-artifactory.dach041.dachser.com/ci/code-coverage-converter:1.0.0 
      description: "Image to use for code quality formatting"

---

coverage-report:
  stage: $[[ inputs.coverage-stage ]]
  image: $[[ inputs.maven-image-name ]]
  script:
    - |
      echo "Starting coverage report generation..."
      
      COVERAGE_FILE="target/site/jacoco/jacoco.xml"
      
      # Always create coverage-report.txt first to ensure artifact exists
      echo "Initializing coverage report..." > coverage-report.txt
      
      if [ ! -f "$COVERAGE_FILE" ]; then
        echo "Warning: JaCoCo XML file not found at $COVERAGE_FILE"
        echo "No JaCoCo coverage reports were generated - skipping coverage processing"
        printf "Total Code Coverage: %s%%\n" "0.00"
        echo "No coverage data available" > coverage-report.txt        
        exit 0
      fi

      # INSTRUCTION coverage      
      instruction_data=$(grep 'type="INSTRUCTION"' "$COVERAGE_FILE" | head -5)
      
      if [ -n "$instruction_data" ]; then        
        coverage_result=$(echo "$instruction_data" | \
          sed -n 's/.*missed="\([0-9]*\)".*covered="\([0-9]*\)".*/\1 \2/p' | \
          awk 'BEGIN{missed=0; covered=0} {missed+=$1; covered+=$2} END {
            total = missed + covered;
            if (total > 0) {
              printf "%.2f", (covered / total) * 100;
            } else {
              printf "0.00";
            }
          }')
        coverage_type="INSTRUCTION"
      else
        echo "No INSTRUCTION coverage found, trying LINE coverage..."
        line_data=$(grep 'type="LINE"' "$COVERAGE_FILE" | head -5)
        echo "Found LINE entries: $(echo "$line_data" | wc -l)"
        
        if [ -n "$line_data" ]; then
          coverage_result=$(echo "$line_data" | \
            sed -n 's/.*missed="\([0-9]*\)".*covered="\([0-9]*\)".*/\1 \2/p' | \
            awk 'BEGIN{missed=0; covered=0} {missed+=$1; covered+=$2} END {
              total = missed + covered;
              if (total > 0) {
                printf "%.2f", (covered / total) * 100;
              } else {
                printf "0.00";
              }
            }')
          coverage_type="LINE"
        else
          echo "No coverage data found"
          coverage_result="0.00"
          coverage_type="NONE"
        fi
      fi
      
      # Calculate coverage and determine status
      coverage_int=$(echo "$coverage_result" | cut -d'.' -f1)      
      
      if [ "$coverage_int" -ge 90 ]; then
        status_text="⭐ Quality: EXCELLENT"
        status_color="\033[1;32m⭐ Quality: EXCELLENT\033[0m"
      elif [ "$coverage_int" -ge 80 ]; then
        status_text="✅ Quality: GOOD"
        status_color="\033[1;32m✅ Quality: GOOD\033[0m"
      elif [ "$coverage_int" -ge 70 ]; then
        status_text="⚠️ Quality: FAIR"
        status_color="\033[1;33m⚠️ Quality: FAIR\033[0m"
      elif [ "$coverage_int" -ge 60 ]; then
        status_text="📉 Quality: POOR"
        status_color="\033[1;33m📉 Quality: POOR\033[0m"
      else
        status_text="❌ Quality: CRITICAL"
        status_color="\033[1;31m❌ Quality: CRITICAL\033[0m"
      fi      
      
      # Use printf to avoid variable expansion issues - clean output for GitLab regex
      printf "Total Code Coverage: %s%%\n" "$coverage_result"
      
      # Print status on a second line - use %b to interpret escape sequences
      printf "Status: %b\n" "$status_color"
      
      # Create coverage report file
      {
        echo "Coverage Type: $coverage_type"
        printf "Total Code Coverage: %s%%\n" "$coverage_result"
        echo "Status: $status_text"
        echo "Generated at: $(date)"
      } > coverage-report.txt

      echo "Coverage report file created successfully"
  needs: 
    - job: "$[[ inputs.report-job-name ]]"
      optional: true
  rules:
    - if: '$CI_COMMIT_TAG'
      when: never
    - when: always
  artifacts:
    paths:
      - target/site/jacoco/
      - coverage-report.txt
    expire_in: $[[ inputs.artifacts-expiry ]]
    when: always
  coverage: '/Total Code Coverage: ([0-9]{1,3}\.[0-9]{2})%/'
  allow_failure: true

# Visualization job that converts JaCoCo XML to Cobertura format for GitLab UI display
coverage-visualize:
  stage: $[[ inputs.visualize-stage ]]
  image: docker-all.dach041.dachser.com/haynes/jacoco2cobertura:1.0.10
  script:
    - |
      mkdir -p target/site
      if [ -f "$[[ inputs.jacoco-xml-path ]]" ]; then
        python /opt/cover2cover.py $[[ inputs.jacoco-xml-path ]] $[[ inputs.source-path ]] > target/site/cobertura.xml
      else
        echo "JaCoCo XML file not found at $[[ inputs.jacoco-xml-path ]]"
        echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?><coverage></coverage>" > target/site/cobertura.xml
      fi
  needs:
    - job: coverage-report
      optional: true
  rules:
    - if: '$CI_COMMIT_TAG'
      when: never
    - when: always
  artifacts:
    paths:
      - target/site/cobertura.xml
    reports:
      coverage_report:
        coverage_format: cobertura
        path: target/site/cobertura.xml
    expire_in: $[[ inputs.artifacts-expiry ]]

# Code quality job that formats coverage data for GitLab UI display
code-quality:
  stage: $[[ inputs.code-quality-stage ]]
  image: $[[ inputs.formatter-image-name ]]
  script:
    - cd $CI_PROJECT_DIR
    - python /app/convert_reports.py
    - ls -la gl-code-quality*.json || echo "No JSON files found"
  artifacts:
    reports:
      codequality: gl-code-quality.json
    paths:
      - gl-code-quality*.json
  rules:
    - if: '$CI_COMMIT_TAG'
      when: never
    - when: always
  allow_failure: true
