spec:
  inputs:
    test-stage:
      default: test
    node-image-name:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/nodejs:22-latest
---
include:
  - local: "/templates/.jfrog.gitlab-ci.yml"

npm-test:
  stage: $[[ inputs.test-stage ]]
  image: $[[ inputs.node-image-name ]]
  variables:
    KUBERNETES_MEMORY_LIMIT: 4Gi
    KUBERNETES_EPHEMERAL_STORAGE_LIMIT: 5Gi
  extends: .jf_npm
  rules:
    - if: $CI_COMMIT_BRANCH || $CI_PIPELINE_SOURCE == "merge_request_event"
  script:
    - |
      if [ ! -d "node_modules" ]; then
        jf npm ci --build-name="${BUILD_NAME}" --build-number="${BUILD_NUMBER}" ;
      fi
    - jf npm run test --if-present -- --no-file-parallelism   
  artifacts:
    reports:
      junit: reports/junit.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
      - reports/
    expire_in: 1 hour
