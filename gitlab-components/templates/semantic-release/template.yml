spec:
  inputs:
    release-stage:
      default: release
    java-node-image-name:
      default: bint-docker-dfe.docker-artifactory.dach041.dachser.com/ci/java-17-node-build-image:latest
---
include:
  - local: "/templates/.jfrog.gitlab-ci.yml"

.semantic-release:
  stage: $[[ inputs.release-stage ]]
  image: $[[ inputs.java-node-image-name ]]
  variables:
    GIT_STRATEGY: clone
    GIT_DEPTH: 0
    NPM_REGISTRY: http://artifactory.dach041.dachser.com/artifactory/api/npm/npm-official/
    DFE_REGISTRY: http://artifactory.dach041.dachser.com/artifactory/api/npm/bint-npm-dfe
  script:
    - git config --global --add safe.directory $CI_PROJECT_DIR
    - git config --global http.emptyAuth true
    - |
      mkdir ~/.npm-global
      npm config set prefix '~/.npm-global'
      export PATH=~/.npm-global/bin:$PATH
    - npm config set -g registry $NPM_REGISTRY
    - npm config set -g @dfe:registry $DFE_REGISTRY
    - npm install -g --save-dev semantic-release $SEMANTIC_RELEASE_CONFIG_PACKAGE
    - semantic-release

semantic-release-java:
  extends: [.semantic-release, .jf_java]
  rules:
    - if: $CI_COMMIT_BRANCH == $ALPHA_BRANCH || (($CI_COMMIT_BRANCH == $BETA_BRANCH) && ($BETA_RELEASES_ACTIVE == "true")) || $CI_COMMIT_BRANCH == $MASTER_BRANCH
      exists:
        - pom.xml
  variables:
    SEMANTIC_RELEASE_CONFIG_PACKAGE: '@dfe/semantic-release-maven-config'


semantic-release-npm:
  extends: [.semantic-release]
  rules:
    - if: $CI_COMMIT_BRANCH == $ALPHA_BRANCH || (($CI_COMMIT_BRANCH == $BETA_BRANCH) && ($BETA_RELEASES_ACTIVE == "true")) || $CI_COMMIT_BRANCH == $MASTER_BRANCH
      exists:
        - package.json
  variables:
    SEMANTIC_RELEASE_CONFIG_PACKAGE: '@dfe/semantic-release-npm-config'
