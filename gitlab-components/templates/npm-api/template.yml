spec:
  inputs:
    stage:
      default: post-test
    build-name:
    build-number:
    node-image-name:
      default: bint-docker-dfe.docker-artifactory.dach041.dachser.com/ci/java-17-node-build-image:latest
    npm-registry:
      default: http://artifactory.dach041.dachser.com/artifactory/api/npm/npm-official/
    dfe-registry:
      default: http://artifactory.dach041.dachser.com/artifactory/api/npm/bint-npm-dfe
    jfrog-project:
      default: bint
---
include:
  - local: "/templates/.jfrog.gitlab-ci.yml"

generate-client-api:
  extends: .generate-client-api

generate-client-api-module-name:
  extends: .generate-client-api
  variables:
    CLIENT_API_NAME: "@dfe/$CI_PROJECT_NAME-module"
    CLIENT_API_DESCRIPTION: "generated api for $CI_PROJECT_NAME with --module-name-first-tag"
    CLIENT_API_GENERATION_OPTIONS: "--module-name-first-tag"

.generate-client-api:
  stage: $[[ inputs.stage ]]
  image: $[[ inputs.node-image-name ]]
  extends: [.jf_npm]
  rules:
    - if: $CI_COMMIT_TAG
  variables:
    CLIENT_API_NAME: "@dfe/$CI_PROJECT_NAME"
    CLIENT_API_DESCRIPTION: "generated api for $CI_PROJECT_NAME"
    CLIENT_API_GENERATION_OPTIONS: ""
  script:
    - apt-get update && apt-get install -y jq
    - npm config set -g registry $[[ inputs.npm-registry ]]
    - npm config set -g @dfe:registry $[[ inputs.dfe-registry ]]
    - npm install @dfe/dfe-frontend-api-template typescript tsup@8.1.0
    - mkdir -p client
    - npx swagger-typescript-api@10.0.2 -p target/classes/specification/openapi.yaml -o ./client -n index.ts -t ./node_modules/@dfe/dfe-frontend-api-template/default --disableStrictSSL $CLIENT_API_GENERATION_OPTIONS
    - npx tsup client/index.ts --format cjs,esm --dts --out-dir client
    - cat $API_PACKAGE_JSON | jq --arg name "$CLIENT_API_NAME" --arg description "$CLIENT_API_DESCRIPTION" --arg version "$CI_COMMIT_TAG" '.name = $name | .description = $description | .version = $version' > client/package.json
    - cd client
    - jf npm publish --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]] --project=$[[ inputs.jfrog-project ]]