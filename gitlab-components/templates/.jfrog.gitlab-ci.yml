---
include:
  - remote: "https://releases.jfrog.io/artifactory/jfrog-cli/gitlab/v2/.setup-jfrog-unix.yml"

.jf_npm:
  before_script:
    - !reference [ .setup_jfrog, script ]
    - |
      jf npm-config \
      --repo-resolve=$ARTIFACTORY_REPO_RESOLVE_NPM \
      --repo-deploy=$ARTIFACTORY_REPO_DEPLOY_NPM
    - jf rt build-add-git $BUILD_NAME $BUILD_NUMBER
  after_script:
    - !reference [ .cleanup_jfrog, script ]


.jf_java:
  before_script:
    - !reference [ .setup_jfrog, script ]
    - |
      jf mvn-config \
      --repo-resolve-releases=$ARTIFACTORY_REPO_RESOLVE_RELEASES \
      --repo-resolve-snapshots=$ARTIFACTORY_REPO_RESOLVE_SNAPSHOTS \
      --repo-deploy-releases=$ARTIFACTORY_REPO_DEPLOY_RELEASES \
      --repo-deploy-snapshots=$ARTIFACTORY_REPO_DEPLOY_SNAPSHOTS
    - jf rt build-add-git $BUILD_NAME $BUILD_NUMBER
  after_script:
    - !reference [ .cleanup_jfrog, script ]