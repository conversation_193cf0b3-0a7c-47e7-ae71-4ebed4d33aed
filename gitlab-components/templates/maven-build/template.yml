spec:
  inputs:
    build-stage:
      default: build
    maven-image-name:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/maven:jdk-21-v1.1.0
    jfrog-project:
      default: bint
---
include:
  - local: "/templates/.jfrog.gitlab-ci.yml"

maven-build:
  stage: $[[ inputs.build-stage ]]
  extends: [.jf_java]
  rules:
    - if: $CI_COMMIT_BRANCH || $CI_PIPELINE_SOURCE == "merge_request_event"
  image: $[[ inputs.maven-image-name ]]
  script:
    - jf mvn -B clean install -Dartifactory.publish.artifacts=false -Dartifactory.publish.buildInfo=false
    - |
      if [ -d "target/surefire-reports" ] && [ -n "$(find target/surefire-reports -name '*.xml' -type f 2>/dev/null)" ]; then
        echo "Test reports found"
      else
        echo "No test reports found, creating placeholder XML"
        mkdir -p target/surefire-reports
        cat > target/surefire-reports/placeholder-test-report.xml << 'EOF'
      <?xml version="1.0" encoding="UTF-8"?>
      <testsuite name="no-tests" tests="0" failures="0" errors="0" skipped="0" time="0">
        <!-- No tests executed in this project -->
      </testsuite>
      EOF
      fi
  artifacts:
    paths:
      - target/oas30-imports/specification/openapi.yaml
      - target
    reports:
      junit: target/surefire-reports/*.xml  
    expire_in: 1 hour

maven-build-push:
  stage: $[[ inputs.build-stage ]]
  image: $[[ inputs.maven-image-name ]]
  extends: [.jf_java]
  rules:
    - if: $CI_COMMIT_TAG
  script:
    - jf mvn -B package deploy -DskipTests
    - jf rt build-publish --project=$[[ inputs.jfrog-project ]]
  artifacts:
    paths:
      - target/oas30-imports/specification/openapi.yaml
      - target/classes/specification/openapi.yaml
      - target/*.jar
    expire_in: 1 hour
