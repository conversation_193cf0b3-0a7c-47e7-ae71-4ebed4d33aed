spec:
  inputs:
    stage:
      default: .pre
    image:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/maven:jdk-21-v1.1.0
---
include:
  - local: "/templates/.jfrog.gitlab-ci.yml"

maven-info:
  stage: $[[ inputs.stage ]]
  extends: [.jf_java]
  rules:
    - if: $CI_COMMIT_BRANCH || $CI_PIPELINE_SOURCE == "merge_request_event" || $CI_COMMIT_TAG
  image: $[[ inputs.image ]]
  script:
    - |
      echo "Collect maven group, artifact id and version"
      
      touch groupId.txt
      touch artifactId.txt
      touch version.txt
      
      jf mvn org.apache.maven.plugins:maven-help-plugin:3.2.0:evaluate -Dexpression=project.groupId -q -DforceStdout --log-file groupId.txt >> test.txt
      jf mvn org.apache.maven.plugins:maven-help-plugin:3.2.0:evaluate -Dexpression=project.artifactId -q -DforceStdout --log-file artifactId.txt >> test.txt
      jf mvn org.apache.maven.plugins:maven-help-plugin:3.2.0:evaluate -Dexpression=project.version -q -DforceStdout --log-file version.txt >> test.txt
      
      echo "MAVEN_GROUP_ID=$(cat groupId.txt)" > info.env
      echo "MAVEN_ARTIFACT_ID=$(cat artifactId.txt)" >> info.env
      echo "MAVEN_VERSION=$(cat version.txt)" >> info.env
      cat info.env
  artifacts:
    reports:
      dotenv: info.env

